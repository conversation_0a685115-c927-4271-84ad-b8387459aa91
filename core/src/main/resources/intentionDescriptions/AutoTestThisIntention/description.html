<html>
<body>
Generates test code for the selected method, class, or code snippet.
This intention helps you quickly create unit tests for your code without having to write them manually.
<p>
    The test generation uses context-aware analysis to create appropriate test cases based on the selected code element's
    functionality and structure.
</p>
<p>
    Simply place your cursor on or select a code element like a method or class, then invoke this intention to generate
    test code automatically.
</p>
<p>
    Example usage:
</p>
<pre><code>
public int add(int a, int b) {
    return a + b;
}
</code></pre>
<!-- tooltip end -->
<p>This intention will generate appropriate test cases that verify the functionality of the selected code.</p>

<p>The test generation analyzes method parameters, return types, and behavior to create comprehensive tests.</p>

<p>You can customize test generation settings through <code>Settings/Preferences | Tools | AutoDev</code>.</p>
</body>
</html>