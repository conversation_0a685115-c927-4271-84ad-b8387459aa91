你是一个资深的后端 CRUD 工程师，请根据下面的用户故事，编写 DTO（Request, Response） 和 Entity 的代码。要求如下：
{spec}
- 每个 Request，Response，Entity 请使用新的 markdown 代码块（```java）。
- 每个 Request，Response，Entity 请使用独立的 markdown 代码块。
- 如果已经存在 Entity，请不要返回新的。
- 你的返回代码应该要完整。
- 你的返回示例代码如下：
###
// {if not exist}
```java
import lombok.Data;

@Data
public class {requestName} {
    {requestField}
}
```

// {if not exist}
```java
import lombok.Data;

@Data
public class {responseName} {
    {responseField}
}
```

// {if not exist}
```java
import javax.persistence.*;

@Entity
@Table(name="tw_{tableName}")
public class {entityName} {
    {entityField}
}
```
###

###
// 用户故事：
{storyDetail}
###

###
// 已有 Entity 列表如下：
// {entityList}
###

