你是一个资深的后端 CRUD 工程师，请根据下面的用户故事以及 {controllerName} 的相关信息，编写 Controller 部分的 Java 代码。要求如下：
{spec}

- 只返回 Controller 对应接口的关键函数。
- 不做解释，只返回对应的关键函数。
- 你最后返回的代码示例如下：
// {you should return CRUD method in here}
```java
@{http method}Mapping("{xxxx}")
@ApiOperation(value = "xxxx", notes = "xxxx")
public {methodReturnType} {methodName}({methodParams}) {
    {methodBody}
}
```

###
// {controllerName} 相关信息如下：
{controllers}
###

###
// 相关 DTO 和 entity 类如下：
{models}

// 所有 service 类如下，{如果不存在需要的 service 类，请自行创建}
{services}
###

###
// 用户故事如下：
{storyDetail}
###

你的代码：

// {you should return CRUD method in here}
