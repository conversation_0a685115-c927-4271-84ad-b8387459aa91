为以下代码编写单元测试。

${context.frameworkContext}

#if( $context.relatedClasses.length() > 0 )
    ${context.relatedClasses}
#end
#if( $context.currentClass.length() > 0 )
以下是当前类的信息：
    ${context.currentClass}
#end
${context.extContext}
以下是待测试的源代码：

```$context.lang
${context.imports}
${context.sourceCode}
```

如果是新文件
#if( $context.isNewFile )
请包含package和import，在此处使用 Markdown 代码块编写方法测试代码：
#else
请包含package和import, 在此处使用 Markdown 代码块编写 ${context.testClassName} 测试代码：
#end

