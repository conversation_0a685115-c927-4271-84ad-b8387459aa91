<idea-plugin package="cc.unitmesh.kotlin">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="org.jetbrains.kotlin"/>
        <module name="cc.unitmesh.idea"/>
    </dependencies>

    <extensions defaultExtensionNs="cc.unitmesh">
        <codeModifier language="kotlin"
                      implementationClass="cc.unitmesh.kotlin.provider.KotlinCodeModifier"/>

        <chatContextProvider implementation="cc.unitmesh.kotlin.provider.KotlinTestContextProvider"/>

        <livingDocumentation language="kotlin"
                             implementationClass="cc.unitmesh.kotlin.provider.KotlinLivingDocumentation"/>

        <testDataBuilder language="kotlin"
                         implementationClass="cc.unitmesh.kotlin.provider.KotlinPsiElementDataBuilder"/>

        <classContextBuilder language="kotlin"
                             implementationClass="cc.unitmesh.kotlin.context.KotlinClassContextBuilder"/>

        <methodContextBuilder language="kotlin"
                              implementationClass="cc.unitmesh.kotlin.context.KotlinMethodContextBuilder"/>

        <fileContextBuilder language="kotlin"
                            implementationClass="cc.unitmesh.kotlin.context.KotlinFileContextBuilder"/>

        <variableContextBuilder language="kotlin"
                                implementationClass="cc.unitmesh.kotlin.context.KotlinVariableContextBuilder"/>

        <relatedClassProvider language="kotlin"
                              implementationClass="cc.unitmesh.kotlin.provider.KotlinRelatedClassProvider"/>

        <chatContextProvider implementation="cc.unitmesh.kotlin.provider.KotlinVersionProvider"/>
        <chatContextProvider implementation="cc.unitmesh.kotlin.provider.KotlinTestContextProvider"/>

        <contextPrompter
                language="kotlin"
                implementation="cc.unitmesh.kotlin.provider.KotlinContextPrompter"/>
        <testContextProvider
                language="kotlin"
                implementation="cc.unitmesh.kotlin.provider.KotlinAutoTestService"/>

        <customPromptProvider
                language="kotlin"
                implementationClass="cc.unitmesh.kotlin.provider.KotlinCustomPromptProvider" />

        <langDictProvider implementation="cc.unitmesh.kotlin.indexer.provider.KotlinLangDictProvider"/>
    </extensions>
</idea-plugin>
